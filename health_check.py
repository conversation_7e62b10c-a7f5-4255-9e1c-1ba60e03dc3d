#!/usr/bin/env python3
"""
Simple health check script for Docker health checks.
"""

import sys
import json
from src.careerjet_mcp_server.server import health_check
import asyncio


async def main():
    """Run health check and exit with appropriate code."""
    try:
        result = await health_check()
        print(json.dumps(result, indent=2))
        
        if result.get("status") == "healthy":
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        print(f"Health check failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

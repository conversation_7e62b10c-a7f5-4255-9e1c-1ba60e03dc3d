"""
Main MCP server implementation for Careerjet job search integration.
"""

import asyncio
import logging
import os
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional, Union
from collections.abc import AsyncIterator
from urllib.parse import parse_qs, urlparse

from mcp.server.fastmcp import FastMCP, Context
from pydantic import BaseModel, Field

from .careerjet_client import CareerjetClient, CareerjetAPIError
from .config import get_config, get_mcp_config
from .models import JobListing, SearchResults, JobSearchFilter
from .exceptions import ValidationError, APIError, CareerjetMCPError
from .validators import InputValidator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global client instance
careerjet_client: Optional[CareerjetClient] = None


def parse_config_from_query(query_string: str) -> Dict[str, Any]:
    """Parse configuration from HTTP query parameters."""
    if not query_string:
        return {}

    parsed = parse_qs(query_string)
    config = {}

    # Extract configuration parameters
    for key, values in parsed.items():
        if values:  # Take the first value if multiple are provided
            value = values[0]

            # Convert specific parameters to appropriate types
            if key in ['rate_limit_requests', 'rate_limit_period', 'cache_ttl', 'timeout']:
                try:
                    config[key] = int(value)
                except ValueError:
                    logger.warning(f"Invalid integer value for {key}: {value}")
                    continue
            else:
                config[key] = value

    return config


def get_config_from_environment() -> Dict[str, Any]:
    """Get configuration from environment variables and query parameters."""
    # Start with default configuration
    config = {
        'affiliate_id': os.getenv('CAREERJET_AFFILIATE_ID', '371d48447450886ce16b718533cca6f2'),
        'locale': os.getenv('CAREERJET_DEFAULT_LOCALE', 'en_GB'),
        'user_agent': os.getenv('CAREERJET_DEFAULT_USER_AGENT',
                               'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
        'user_ip': os.getenv('CAREERJET_DEFAULT_USER_IP', '127.0.0.1'),
        'base_url': os.getenv('CAREERJET_DEFAULT_URL', 'https://your-app.com'),
        'rate_limit_requests': int(os.getenv('CAREERJET_RATE_LIMIT_REQUESTS', '100')),
        'rate_limit_period': int(os.getenv('CAREERJET_RATE_LIMIT_PERIOD', '3600')),
        'cache_ttl': int(os.getenv('CAREERJET_CACHE_TTL', '300')),
        'timeout': int(os.getenv('CAREERJET_TIMEOUT', '30'))
    }

    # Override with query parameters if provided (for HTTP transport)
    query_string = os.getenv('QUERY_STRING', '')
    if query_string:
        query_config = parse_config_from_query(query_string)
        config.update(query_config)

    return config


class JobSearchRequest(BaseModel):
    """Request model for job search tool."""
    keywords: Optional[str] = Field(None, description="Keywords to search for in job titles and descriptions")
    location: Optional[str] = Field(None, description="Location to search for jobs (city, country, etc.)")
    sort: str = Field("relevance", description="Sort order: 'relevance', 'date', or 'salary'")
    page: int = Field(1, description="Page number for pagination (starts from 1)")
    pagesize: int = Field(20, description="Number of jobs per page (1-100)")
    contracttype: Optional[str] = Field(None, description="Contract type: 'p' (permanent), 'c' (contract), 't' (temporary), 'i' (training), 'v' (voluntary)")
    contractperiod: Optional[str] = Field(None, description="Contract period: 'f' (full-time), 'p' (part-time)")


class JobSearchResponse(BaseModel):
    """Response model for job search results."""
    total_hits: int = Field(..., description="Total number of jobs found")
    total_pages: int = Field(..., description="Total number of pages available")
    current_page: int = Field(..., description="Current page number")
    jobs: List[JobListing] = Field(..., description="List of job listings")
    has_more_pages: bool = Field(..., description="Whether there are more pages available")


class JobFilterRequest(BaseModel):
    """Request model for advanced job filtering."""
    company_names: Optional[List[str]] = Field(None, description="Filter by specific company names")
    exclude_keywords: Optional[List[str]] = Field(None, description="Keywords to exclude from results")
    remote_only: Optional[bool] = Field(None, description="Filter for remote jobs only")


@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[Dict[str, Any]]:
    """Manage application lifecycle with Careerjet client."""
    global careerjet_client

    # Get configuration from environment and query parameters
    env_config = get_config_from_environment()

    # Create CareerjetConfig from the environment configuration
    from .models import CareerjetConfig
    config = CareerjetConfig(
        affiliate_id=env_config['affiliate_id'],
        locale=env_config['locale'],
        default_user_agent=env_config['user_agent'],
        default_user_ip=env_config['user_ip'],
        default_url=env_config['base_url'],
        rate_limit_requests=env_config['rate_limit_requests'],
        rate_limit_period=env_config['rate_limit_period'],
        cache_ttl=env_config['cache_ttl'],
        timeout=env_config['timeout']
    )

    careerjet_client = CareerjetClient(config)

    logger.info(f"Starting Careerjet MCP Server with locale: {config.locale}")
    logger.info(f"Configuration: affiliate_id={config.affiliate_id[:8]}..., locale={config.locale}")

    try:
        # Initialize the client
        await careerjet_client.__aenter__()
        yield {"careerjet_client": careerjet_client}
    finally:
        # Cleanup on shutdown
        if careerjet_client:
            await careerjet_client.__aexit__(None, None, None)
        logger.info("Careerjet MCP Server shutdown complete")


# Get MCP configuration
mcp_config = get_mcp_config()

# Create FastMCP server with lifespan management
app = FastMCP(
    name=mcp_config["name"],
    lifespan=app_lifespan
)


@app.tool(title="Search Jobs")
async def search_jobs(request: JobSearchRequest, ctx: Context) -> JobSearchResponse:
    """
    Search for jobs using the Careerjet API.

    This tool allows you to search for job listings based on keywords, location,
    and various filters. Results are paginated and can be sorted by relevance,
    date, or salary.
    """
    try:
        # Validate input parameters
        validated_keywords = InputValidator.validate_keywords(request.keywords)
        validated_location = InputValidator.validate_location(request.location)
        validated_sort = InputValidator.validate_sort(request.sort)
        validated_page = InputValidator.validate_page(request.page)
        validated_pagesize = InputValidator.validate_pagesize(request.pagesize)
        validated_contracttype = InputValidator.validate_contract_type(request.contracttype)
        validated_contractperiod = InputValidator.validate_contract_period(request.contractperiod)

        # Get the client from context
        lifespan_ctx = ctx.request_context.lifespan_context
        client: CareerjetClient = lifespan_ctx["careerjet_client"]

        # Perform the search with validated parameters
        results = await client.search_jobs(
            keywords=validated_keywords,
            location=validated_location,
            sort=validated_sort,
            page=validated_page,
            pagesize=validated_pagesize,
            contracttype=validated_contracttype,
            contractperiod=validated_contractperiod
        )

        ctx.info(f"Found {results.total_hits} jobs matching search criteria")

        return JobSearchResponse(
            total_hits=results.total_hits,
            total_pages=results.total_pages,
            current_page=results.current_page,
            jobs=results.jobs,
            has_more_pages=results.has_more_pages
        )

    except ValidationError as e:
        ctx.error(f"Validation error: {e.message}")
        raise ValueError(f"Invalid input: {e.message}")
    except CareerjetAPIError as e:
        ctx.error(f"Careerjet API error: {e}")
        raise ValueError(f"Job search failed: {e}")
    except Exception as e:
        ctx.error(f"Unexpected error during job search: {e}")
        raise ValueError(f"Job search failed: {e}")


@app.tool(title="Search Jobs with Advanced Filters")
async def search_jobs_filtered(
    search: JobSearchRequest,
    filters: JobFilterRequest,
    ctx: Context
) -> JobSearchResponse:
    """
    Search for jobs with advanced filtering options.
    
    This tool extends the basic job search with additional filtering capabilities
    such as filtering by company names, excluding specific keywords, or finding
    only remote jobs.
    """
    try:
        # Get the client from context
        lifespan_ctx = ctx.request_context.lifespan_context
        client: CareerjetClient = lifespan_ctx["careerjet_client"]
        
        # Create filter object
        job_filters = JobSearchFilter(
            company_names=filters.company_names,
            exclude_keywords=filters.exclude_keywords,
            remote_only=filters.remote_only
        )
        
        # Perform the search with filters
        results = await client.search_jobs(
            keywords=search.keywords,
            location=search.location,
            sort=search.sort,
            page=search.page,
            pagesize=search.pagesize,
            contracttype=search.contracttype,
            contractperiod=search.contractperiod,
            filters=job_filters
        )
        
        ctx.info(f"Found {results.total_hits} jobs matching search criteria with filters")
        
        return JobSearchResponse(
            total_hits=results.total_hits,
            total_pages=results.total_pages,
            current_page=results.current_page,
            jobs=results.jobs,
            has_more_pages=results.has_more_pages
        )
        
    except CareerjetAPIError as e:
        ctx.error(f"Careerjet API error: {e}")
        raise ValueError(f"Filtered job search failed: {e}")
    except Exception as e:
        ctx.error(f"Unexpected error during filtered job search: {e}")
        raise ValueError(f"Filtered job search failed: {e}")


@app.tool(title="Get Job Summary")
async def get_job_summary(job_id: str, ctx: Context) -> JobListing:
    """
    Get summary information for a specific job by ID.
    
    Note: Since Careerjet API doesn't provide a direct job details endpoint,
    this tool returns cached job information if available.
    """
    try:
        # Get the client from context
        lifespan_ctx = ctx.request_context.lifespan_context
        client: CareerjetClient = lifespan_ctx["careerjet_client"]
        
        # Try to get job details (this will return None for Careerjet)
        job_details = await client.get_job_details(job_id)
        
        if job_details:
            ctx.info(f"Retrieved details for job: {job_id}")
            return job_details
        else:
            ctx.warning(f"Job details not available for job ID: {job_id}")
            raise ValueError(f"Job details not available for job ID: {job_id}. Use the job URL from search results to view full details.")
            
    except CareerjetAPIError as e:
        ctx.error(f"Careerjet API error: {e}")
        raise ValueError(f"Failed to get job details: {e}")
    except Exception as e:
        ctx.error(f"Unexpected error getting job details: {e}")
        raise ValueError(f"Failed to get job details: {e}")


@app.tool(title="Clear Search Cache")
async def clear_cache(ctx: Context) -> str:
    """
    Clear the job search cache to ensure fresh results.
    
    This tool clears the internal cache used to store search results,
    which can be useful when you want to ensure you're getting the
    most up-to-date job listings.
    """
    try:
        # Get the client from context
        lifespan_ctx = ctx.request_context.lifespan_context
        client: CareerjetClient = lifespan_ctx["careerjet_client"]
        
        client.clear_cache()
        ctx.info("Job search cache cleared successfully")
        return "Cache cleared successfully. Next searches will fetch fresh results."
        
    except Exception as e:
        ctx.error(f"Error clearing cache: {e}")
        raise ValueError(f"Failed to clear cache: {e}")


@app.tool(title="Get Cache Statistics")
async def get_cache_stats(ctx: Context) -> Dict[str, Any]:
    """
    Get statistics about the job search cache.

    This tool provides information about cache usage, including the number
    of cached entries and cache hit rates.
    """
    try:
        # Get the client from context
        lifespan_ctx = ctx.request_context.lifespan_context
        client: CareerjetClient = lifespan_ctx["careerjet_client"]

        stats = client.get_cache_stats()
        ctx.info("Retrieved cache statistics")
        return stats

    except Exception as e:
        ctx.error(f"Error getting cache stats: {e}")
        raise ValueError(f"Failed to get cache statistics: {e}")


# Resource endpoints for job data access
@app.resource("careerjet://search/{keywords}")
async def get_search_results(keywords: str, ctx: Context) -> str:
    """
    Get job search results as a resource.

    This resource provides job search results for the specified keywords
    in a formatted text representation suitable for LLM context.
    """
    try:
        # Get the client from context
        lifespan_ctx = ctx.request_context.lifespan_context
        client: CareerjetClient = lifespan_ctx["careerjet_client"]

        # Perform search with default parameters
        results = await client.search_jobs(keywords=keywords, pagesize=10)

        # Format results as text
        output = f"Job Search Results for '{keywords}'\n"
        output += f"Total jobs found: {results.total_hits}\n"
        output += f"Showing page {results.current_page} of {results.total_pages}\n\n"

        for i, job in enumerate(results.jobs, 1):
            output += f"{i}. {job.title}\n"
            output += f"   Company: {job.company}\n"
            output += f"   Location: {job.location}\n"
            if job.salary:
                output += f"   Salary: {job.salary}\n"
            output += f"   URL: {job.url}\n"
            output += f"   Description: {job.description[:200]}...\n\n"

        ctx.info(f"Retrieved search results resource for keywords: {keywords}")
        return output

    except CareerjetAPIError as e:
        ctx.error(f"Careerjet API error in resource: {e}")
        return f"Error retrieving job search results: {e}"
    except Exception as e:
        ctx.error(f"Unexpected error in search resource: {e}")
        return f"Error retrieving job search results: {e}"


@app.resource("careerjet://search/{keywords}/location/{location}")
async def get_location_search_results(keywords: str, location: str, ctx: Context) -> str:
    """
    Get job search results for specific keywords and location as a resource.

    This resource provides job search results filtered by both keywords and
    location in a formatted text representation.
    """
    try:
        # Get the client from context
        lifespan_ctx = ctx.request_context.lifespan_context
        client: CareerjetClient = lifespan_ctx["careerjet_client"]

        # Perform search with location filter
        results = await client.search_jobs(keywords=keywords, location=location, pagesize=10)

        # Format results as text
        output = f"Job Search Results for '{keywords}' in '{location}'\n"
        output += f"Total jobs found: {results.total_hits}\n"
        output += f"Showing page {results.current_page} of {results.total_pages}\n\n"

        for i, job in enumerate(results.jobs, 1):
            output += f"{i}. {job.title}\n"
            output += f"   Company: {job.company}\n"
            output += f"   Location: {job.location}\n"
            if job.salary:
                output += f"   Salary: {job.salary}\n"
            output += f"   URL: {job.url}\n"
            output += f"   Description: {job.description[:200]}...\n\n"

        ctx.info(f"Retrieved location search results resource for: {keywords} in {location}")
        return output

    except CareerjetAPIError as e:
        ctx.error(f"Careerjet API error in location resource: {e}")
        return f"Error retrieving job search results: {e}"
    except Exception as e:
        ctx.error(f"Unexpected error in location search resource: {e}")
        return f"Error retrieving job search results: {e}"


@app.resource("careerjet://recent/{location}")
async def get_recent_jobs(location: str, ctx: Context) -> str:
    """
    Get recent job postings for a specific location as a resource.

    This resource provides the most recently posted jobs in the specified
    location, sorted by date.
    """
    try:
        # Get the client from context
        lifespan_ctx = ctx.request_context.lifespan_context
        client: CareerjetClient = lifespan_ctx["careerjet_client"]

        # Search for recent jobs sorted by date
        results = await client.search_jobs(location=location, sort="date", pagesize=15)

        # Format results as text
        output = f"Recent Job Postings in '{location}'\n"
        output += f"Total jobs found: {results.total_hits}\n"
        output += f"Showing {len(results.jobs)} most recent jobs\n\n"

        for i, job in enumerate(results.jobs, 1):
            output += f"{i}. {job.title}\n"
            output += f"   Company: {job.company}\n"
            output += f"   Location: {job.location}\n"
            if job.date_posted:
                output += f"   Posted: {job.date_posted}\n"
            if job.salary:
                output += f"   Salary: {job.salary}\n"
            output += f"   URL: {job.url}\n"
            output += f"   Description: {job.description[:150]}...\n\n"

        ctx.info(f"Retrieved recent jobs resource for location: {location}")
        return output

    except CareerjetAPIError as e:
        ctx.error(f"Careerjet API error in recent jobs resource: {e}")
        return f"Error retrieving recent job postings: {e}"
    except Exception as e:
        ctx.error(f"Unexpected error in recent jobs resource: {e}")
        return f"Error retrieving recent job postings: {e}"


@app.resource("careerjet://config")
async def get_server_config(ctx: Context) -> str:
    """
    Get server configuration information as a resource.

    This resource provides information about the current server configuration,
    including API settings and available locales.
    """
    try:
        config = get_config()
        mcp_config = get_mcp_config()

        output = f"Careerjet MCP Server Configuration\n"
        output += f"=====================================\n\n"
        output += f"Server Name: {mcp_config['name']}\n"
        output += f"Server Version: {mcp_config['version']}\n"
        output += f"Log Level: {mcp_config['log_level']}\n\n"
        output += f"Careerjet API Configuration:\n"
        output += f"- Affiliate ID: {config.affiliate_id}\n"
        output += f"- Locale: {config.locale}\n"
        output += f"- Rate Limit: {config.rate_limit_requests} requests per {config.rate_limit_period} seconds\n"
        output += f"- Cache TTL: {config.cache_ttl} seconds\n"
        output += f"- Request Timeout: {config.timeout} seconds\n\n"
        output += f"Available Contract Types:\n"
        output += f"- p: Permanent\n"
        output += f"- c: Contract\n"
        output += f"- t: Temporary\n"
        output += f"- i: Training\n"
        output += f"- v: Voluntary\n\n"
        output += f"Available Contract Periods:\n"
        output += f"- f: Full-time\n"
        output += f"- p: Part-time\n\n"
        output += f"Available Sort Options:\n"
        output += f"- relevance: Sort by relevance (default)\n"
        output += f"- date: Sort by posting date\n"
        output += f"- salary: Sort by salary\n"

        ctx.info("Retrieved server configuration resource")
        return output

    except Exception as e:
        ctx.error(f"Error getting server config: {e}")
        return f"Error retrieving server configuration: {e}"


@app.resource("careerjet://health")
async def get_health_status(ctx: Context) -> str:
    """
    Get server health status as a resource.

    This resource provides health check information including server status,
    client initialization state, and basic connectivity.
    """
    try:
        # Check if client is initialized
        client_status = "initialized" if careerjet_client is not None else "not initialized"

        # Basic health information
        output = f"Careerjet MCP Server Health Status\n"
        output += f"==================================\n\n"
        output += f"Status: healthy\n"
        output += f"Service: careerjet-mcp-server\n"
        output += f"Client Status: {client_status}\n"
        output += f"Transport Mode: {os.getenv('MCP_TRANSPORT', 'stdio')}\n"

        # If client is available, try to get cache stats as a connectivity test
        if careerjet_client:
            try:
                cache_stats = careerjet_client.get_cache_stats()
                output += f"Cache Entries: {cache_stats.get('total_entries', 0)}\n"
                output += f"Cache Hits: {cache_stats.get('hits', 0)}\n"
                output += f"Cache Misses: {cache_stats.get('misses', 0)}\n"
            except Exception as cache_error:
                output += f"Cache Status: Error - {cache_error}\n"

        ctx.info("Retrieved health status resource")
        return output

    except Exception as e:
        ctx.error(f"Error getting health status: {e}")
        return f"Error retrieving health status: {e}"


def main() -> None:
    """Main entry point for the server."""
    try:
        # Log transport mode for debugging
        transport_mode = os.getenv('MCP_TRANSPORT', 'stdio')
        logger.info(f"Starting server in {transport_mode} mode")

        app.run()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise


if __name__ == "__main__":
    main()
